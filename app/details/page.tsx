'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageTransition } from '@/components/ui/page-transition';
import { useBooking } from '@/contexts/BookingContext';
import { motion } from 'framer-motion';
import { User, Home as HomeIcon, Search, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';

// Student interface based on database schema
interface Student {
  id: string;
  name: string;
  admission_number: string;
  hostel: string | null;
  route: string | null;
  created_at: string;
  updated_at: string;
}

export default function DetailsPage() {
  const [isCheckingBooking, setIsCheckingBooking] = useState(true);
  const [admissionNumber, setAdmissionNumber] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [student, setStudent] = useState<Student | null>(null);
  const [searchError, setSearchError] = useState('');
  const router = useRouter();
  const { updateBookingData } = useBooking();

  // Validation function for admission number format
  const validateAdmissionNumber = (value: string): boolean => {
    if (value.length < 7 || value.length > 9) return false;
    const regex = /^\d{2}[A-Z]{2,6}\d{3}$/;
    return regex.test(value);
  };

  // Handle admission number input change
  const handleAdmissionNumberChange = (value: string) => {
    // Convert to uppercase and remove invalid characters
    const sanitized = value.toUpperCase().replace(/[^A-Z0-9]/g, '');

    // Limit length to 9 characters
    const limited = sanitized.slice(0, 9);

    setAdmissionNumber(limited);
    setSearchError('');
    setStudent(null);
  };

  // Search for student by admission number
  const searchStudent = async () => {
    if (!validateAdmissionNumber(admissionNumber)) {
      setSearchError('Invalid admission number format');
      return;
    }

    setIsSearching(true);
    setSearchError('');

    try {
      const response = await fetch(`/api/students/lookup?admission_number=${admissionNumber}`);
      const result = await response.json();

      if (result.success) {
        // Normalize admission number field so it is always available for display
        const data = result.data || {};
        const normalizedStudent = {
          ...data,
          admission_number:
            data.admission_number ||
            data.admissionNumber ||
            data.admissionNo ||
            data.adm_no ||
            admissionNumber ||
            '',
        };

        setStudent(normalizedStudent as Student);
        toast.success('Student found successfully!');
      } else {
        setSearchError(result.error || 'Student not found');
        setStudent(null);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchError('Failed to search for student. Please try again.');
      setStudent(null);
    } finally {
      setIsSearching(false);
    }
  };

  // Proceed to next step
  const handleNext = () => {
    if (!student) return;

    // Update booking context with student information
    updateBookingData({
      studentName: student.name,
      admissionNumber: student.admission_number,
    });

    router.push('/buses');
  };

  // Check booking status on component mount
  useEffect(() => {
    const checkBookingStatus = async () => {
      try {
        // Add cache-busting by using timestamp and no-cache options
        const timestamp = Date.now();
        const response = await fetch(`/api/booking-status?t=${timestamp}`, {
          cache: 'no-store',
          headers: {
            'Pragma': 'no-cache'
          }
        });
        const data = await response.json();

        if (!data.enabled) {
          toast.error('Booking is currently disabled. Please try again later.');
          router.push('/');
          return;
        }

        setIsCheckingBooking(false);
      } catch (error) {
        console.error('Error checking booking status:', error);
        toast.error('Unable to check booking status. Please try again.');
        router.push('/');
      }
    };

    checkBookingStatus();
  }, [router]);

  // Show loading state while checking booking status
  if (isCheckingBooking) {
    return (
      <PageTransition direction="right">
        <div className="min-h-screen flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-md"
          >
            <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Checking booking availability...</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition direction="right">
      <div className="min-h-screen flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-sm">
            <CardHeader className="text-center bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-t-lg">
              <CardTitle className="text-2xl font-bold flex items-center justify-center gap-2">
                <User className="w-6 h-6" />
                Student Details
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              <div className="space-y-6">
                {/* Admission Number Input */}
                <div className="space-y-2">
                  <label className="text-gray-700 font-semibold text-sm">
                    Admission Number
                  </label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="e.g., 24CS094, 24MCA094, 25IMCA121"
                      value={admissionNumber}
                      onChange={(e) => handleAdmissionNumberChange(e.target.value)}
                      className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      disabled={isSearching}
                    />
                    <Button
                      onClick={searchStudent}
                      disabled={!validateAdmissionNumber(admissionNumber) || isSearching}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4"
                    >
                      {isSearching ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      ) : (
                        <Search className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                  <p className="text-sm text-gray-500">
                    Format: 2 digits + uppercase letters + 3 digits (7-9 characters)
                  </p>
                  {searchError && (
                    <p className="text-sm text-red-600">{searchError}</p>
                  )}
                </div>

                {/* Student Details Display */}
                {student && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-green-50 border border-green-200 rounded-lg p-4 space-y-3"
                  >
                    <div className="flex items-center gap-2 text-green-700 font-semibold">
                      <CheckCircle className="w-5 h-5" />
                      Student Found
                    </div>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Name:</span>
                        <span className="ml-2 text-gray-900">{student.name}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Admission Number:</span>
                        <span className="ml-2 text-gray-900">{student.admission_number}</span>
                      </div>
                      {student.hostel && (
                        <div>
                          <span className="font-medium text-gray-700">Hostel:</span>
                          <span className="ml-2 text-gray-900">{student.hostel}</span>
                        </div>
                      )}
                      {student.route && (
                        <div>
                          <span className="font-medium text-gray-700">Route:</span>
                          <span className="ml-2 text-gray-900">{student.route}</span>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}

                {/* Next Button */}
                {student && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <Button
                      onClick={handleNext}
                      className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
                    >
                      Continue to Bus Selection
                    </Button>
                  </motion.div>
                )}
              </div>
            </CardContent>
          </Card>
          
          {/* Home button below the card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mt-4"
          >
            <Button
              onClick={() => router.push('/')}
              variant="secondary"
              className="w-full bg-white border border-blue-600 rounded text-gray-800 hover:bg-gray-200"
            >
              <HomeIcon className="w-4 h-4 mr-2" />
              Home
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </PageTransition>
  );
}