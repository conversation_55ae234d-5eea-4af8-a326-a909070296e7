# Bus Name Integration in Bookings

## Overview

This enhancement adds a `bus_name` column to the `bookings` table to store the bus name that corresponds to the route code. This provides a direct reference to the bus name without requiring a join query, improving performance and data accessibility.

## Database Changes

### Migration: `20250114000000_add_bus_name_to_bookings.sql`

- **Added column:**
  - `bus_name` (VARCHAR(100), nullable) - The name of the bus corresponding to the route_code, retrieved from buses table

- **Added indexes:**
  - `idx_bookings_bus_name` - For efficient queries on bus_name field
  - `idx_bookings_route_bus_name` - Composite index for bus-related queries

- **Data migration:**
  - Updates existing bookings with bus names from the buses table

## API Changes

### Booking Creation (`/api/bookings`)

**Before:**
```typescript
// Booking creation without bus name
const bookingData = {
  admission_number: admissionNumber,
  student_name: studentName,
  bus_route: busRoute,
  destination: destination,
  payment_status: paymentStatus,
  // ... other fields
};
```

**After:**
```typescript
// Fetch bus name from buses table based on route code
const { data: busData, error: busError } = await supabaseAdmin
  .from('buses')
  .select('name')
  .eq('route_code', busRoute)
  .eq('is_active', true)
  .single();

if (busError || !busData) {
  return NextResponse.json({ 
    error: 'Failed to fetch bus information',
    details: `No bus found for route code ${busRoute}`
  }, { status: 400 });
}

// Include bus name in booking creation
const bookingData = {
  admission_number: admissionNumber,
  student_name: studentName,
  bus_route: busRoute,
  destination: destination,
  payment_status: paymentStatus,
  bus_name: busData.name,  // NEW
  // ... other fields
};
```

### Booking Search (`/api/bookings/search`)

The booking search API automatically includes the `bus_name` field in the response since it uses `select('*')`:

```json
{
  "success": true,
  "data": {
    "bookings": [
      {
        "id": 1,
        "admission_number": "22CS001",
        "student_name": "John Doe",
        "bus_route": "bus-7",
        "bus_name": "Bus 7 - Kannur Route",  // NEW
        "destination": "Kannur",
        "go_date": "2024-01-15",
        "return_date": "2024-01-20",
        "fare": 140.00,
        "payment_status": true,
        "created_at": "2024-01-10T10:30:00Z"
      }
    ],
    "count": 1
  }
}
```

## TypeScript Interface Updates

### Booking Interface

```typescript
export interface Booking {
  studentName: string;
  admissionNumber: string;
  busRoute: string;
  destination: string;
  paymentStatus: boolean;
  timestamp: string;
  goDate?: string | null;
  returnDate?: string | null;
  fare?: number | null;
  busName?: string | null;  // NEW
}
```

### Supabase Database Types

```typescript
bookings: {
  Row: {
    id: number;
    admission_number: string;
    student_name: string;
    bus_route: string;
    destination: string;
    payment_status: boolean;
    go_date: string | null;
    return_date: string | null;
    fare: number | null;
    bus_name: string | null;  // NEW
    razorpay_payment_id: string | null;
    razorpay_order_id: string | null;
    razorpay_signature: string | null;
    created_at: string;
  };
  Insert: {
    // ... same fields as Row with optional types
    bus_name?: string | null;  // NEW
  };
  Update: {
    // ... same fields as Row with optional types
    bus_name?: string | null;  // NEW
  };
}
```

## Implementation Flow

### Example Flow:
1. **Booking Request**: User creates booking with `route_code: "bus-7"`
2. **Bus Lookup**: System queries `SELECT name FROM buses WHERE route_code = "bus-7"`
3. **Bus Found**: Returns `"Bus 7 - Kannur Route"`
4. **Booking Creation**: Stores bus name in `bus_name` column
5. **Response**: Booking includes both `bus_route` and `bus_name`

### Error Handling:
- If no matching bus is found for the route code, the booking creation fails with a 400 error
- Only active buses (`is_active = true`) are considered for the lookup
- Proper error messages guide users to valid route codes

## Benefits

1. **Performance**: Eliminates need for JOIN queries when displaying booking information
2. **Data Integrity**: Ensures bus name consistency even if bus names change later
3. **User Experience**: Provides immediate access to bus names in booking displays
4. **Reporting**: Enables efficient reporting and filtering by bus names
5. **Audit Trail**: Preserves historical bus names for bookings

## Usage Examples

### Querying Bookings by Bus Name

```sql
-- Find all bookings for a specific bus
SELECT * FROM bookings WHERE bus_name = 'Bus 7 - Kannur Route';

-- Count bookings by bus name
SELECT bus_name, COUNT(*) as booking_count 
FROM bookings 
GROUP BY bus_name 
ORDER BY booking_count DESC;

-- Find bookings with specific bus name pattern
SELECT * FROM bookings 
WHERE bus_name LIKE '%Kannur%';
```

### Frontend Display

```typescript
// Display booking with bus name
const BookingCard = ({ booking }) => (
  <div>
    <h3>Booking Details</h3>
    <p>Student: {booking.student_name}</p>
    <p>Bus: {booking.bus_name}</p>  {/* Direct access, no additional query needed */}
    <p>Route: {booking.bus_route}</p>
    <p>Destination: {booking.destination}</p>
  </div>
);
```

## Testing

Use the provided test script to verify the implementation:

```bash
node scripts/test-bus-name-integration.js
```

This script will:
1. Check if the `bus_name` column exists
2. Verify buses table has data
3. Create a test booking with bus name lookup
4. Verify the bus name was stored correctly
5. Test that booking search includes bus name
6. Clean up test data

## Migration Notes

- The migration adds a nullable column, so existing bookings will have `NULL` values for `bus_name`
- New bookings created after the migration will automatically include the bus name from the buses table
- The migration includes a data update to populate `bus_name` for existing bookings
- Indexes are created for optimal query performance on the new field

## Security Considerations

- Bus name lookup only considers active buses (`is_active = true`)
- Proper error handling prevents booking creation with invalid route codes
- Input validation ensures route codes match existing bus records
- No sensitive information is exposed through bus names 