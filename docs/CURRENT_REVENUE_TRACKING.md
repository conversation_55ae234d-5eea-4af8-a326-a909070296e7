# Current Revenue Tracking System

## Overview

The Current Revenue Tracking System adds comprehensive revenue tracking functionality to the bus booking application. It automatically tracks total revenue from paid bookings and provides real-time revenue statistics in the admin dashboard.

## Features

### 1. Database Schema Changes

#### Admin Settings Table
- **`current_revenue`**: New column to track total revenue from paid bookings
- **Type**: DECIMAL(10,2) - Supports up to 10 digits with 2 decimal places
- **Default Value**: 0.00
- **Constraints**: NOT NULL, CHECK (current_revenue >= 0)
- **Purpose**: Stores cumulative revenue from all paid bookings since last reset

### 2. Automatic Revenue Tracking

#### Database Trigger Integration
- **Function**: `handle_new_booking()` - Updated to include revenue tracking
- **Trigger**: `booking_statistics_trigger` - Automatically fires on new booking insertions
- **Logic**: 
  - Only increments revenue for bookings with `payment_status = true`
  - Adds the booking's `fare` amount to `current_revenue`
  - Uses COALESCE to handle null values safely
  - Updates `updated_at` timestamp

#### Revenue Calculation
```sql
-- For paid bookings only
IF NEW.payment_status = true THEN
  UPDATE admin_settings 
  SET current_revenue = COALESCE(current_revenue, 0.00) + COALESCE(NEW.fare, 0.00),
      updated_at = NOW()
  WHERE id = 1;
END IF;
```

### 3. Reset Functionality

#### Database Function Updates
- **Function**: `reset_all_bookings()` - Updated to include revenue reset
- **Action**: Sets `current_revenue` to 0.00 when called
- **Integration**: Works with existing reset button in admin dashboard
- **Logging**: Includes revenue reset in operation logs

#### Reset Process
```sql
UPDATE admin_settings 
SET current_bookings = 0,
    paid_bookings = 0,
    unpaid_bookings = 0,
    current_revenue = 0.00,  -- NEW: Reset revenue
    updated_at = NOW()
WHERE id = 1;
```

### 4. Statistics Integration

#### Database Function Updates
- **Function**: `get_detailed_booking_statistics()` - Updated to include revenue
- **Return Value**: Added `current_revenue DECIMAL(10,2)` to return table
- **Query**: Fetches current revenue from admin_settings table

#### API Endpoint Updates
- **Endpoint**: `/api/admin/analytics/detailed-stats`
- **Response**: Includes `currentRevenue` field in JSON response
- **Fallback**: Handles cases where database function is unavailable
- **Format**: Returns revenue as number for frontend formatting

### 5. Admin Dashboard Integration

#### Frontend Updates
- **Component**: Admin dashboard statistics display
- **Layout**: Added revenue card to statistics grid
- **Formatting**: Displays revenue with ₹ symbol and 2 decimal places
- **Styling**: Green background to highlight revenue information

#### Statistics Display
```typescript
<div className="text-center p-3 bg-green-50 rounded-lg">
  <div className="text-xl font-bold text-green-600">
    ₹{newBookingStats.currentRevenue.toFixed(2)}
  </div>
  <div className="text-sm text-gray-600">Current Revenue</div>
</div>
```

### 6. TypeScript Integration

#### Type Definitions
- **Interface**: `NewBookingStats` - Added `currentRevenue: number`
- **Database Types**: Updated Supabase types to include `current_revenue`
- **Consistency**: Follows existing patterns for type safety

#### Type Updates
```typescript
export interface NewBookingStats {
  totalBuses: number;
  totalBookings: number;
  currentBookings: number;
  paidBookings: number;
  unpaidBookings: number;
  currentRevenue: number;  // NEW
  availableSeats: number;
  totalCapacity: number;
  occupancyRate: string;
}
```

## Database Migration

### Migration File: `20250116000000_add_current_revenue_to_admin_settings.sql`

#### Schema Changes
1. **Add Column**: `current_revenue DECIMAL(10,2) DEFAULT 0.00 NOT NULL CHECK (current_revenue >= 0)`
2. **Initialize Data**: Set existing records to 0.00 if null
3. **Calculate Initial Revenue**: Sum existing paid bookings' fares

#### Function Updates
1. **handle_new_booking()**: Add revenue increment logic
2. **reset_all_bookings()**: Add revenue reset logic  
3. **get_detailed_booking_statistics()**: Add revenue to return table

#### Initial Data Population
```sql
-- Calculate and populate initial revenue from existing bookings
UPDATE admin_settings 
SET current_revenue = COALESCE(
  (SELECT SUM(COALESCE(fare, 0)) FROM bookings WHERE payment_status = true), 
  0.00
)
WHERE id = 1;
```

## API Endpoints

### Detailed Statistics API
- **URL**: `/api/admin/analytics/detailed-stats`
- **Method**: GET
- **Response**: Includes `currentRevenue` field
- **Caching**: 30-second cache with 15-second stale-while-revalidate

### Reset API
- **URL**: `/api/admin/analytics/reset`
- **Method**: POST
- **Action**: Calls `reset_all_bookings()` function
- **Effect**: Resets revenue to 0.00 along with other statistics

## Testing

### Test Script: `scripts/test-current-revenue.js`

#### Test Coverage
1. **Column Existence**: Verify `current_revenue` column exists
2. **Function Updates**: Check trigger and reset functions
3. **Statistics Integration**: Verify revenue in statistics function
4. **Booking Creation**: Test revenue increment on new paid booking
5. **API Endpoints**: Verify revenue in API responses
6. **Cleanup**: Remove test data after verification

#### Test Execution
```bash
node scripts/test-current-revenue.js
```

## Usage Examples

### Viewing Current Revenue
1. Navigate to Admin Dashboard
2. Revenue is displayed in the statistics section
3. Format: ₹1,234.56 (with proper currency formatting)

### Resetting Revenue
1. Click "Reset Seating" button in admin dashboard
2. Confirm the reset operation
3. Revenue will be reset to ₹0.00 along with other statistics

### Revenue Tracking
- Revenue automatically increments when paid bookings are created
- Only paid bookings (`payment_status = true`) contribute to revenue
- Revenue persists until manually reset
- Historical tracking even if individual bookings are deleted

## Security Considerations

### Data Integrity
- **Constraints**: CHECK constraint ensures revenue cannot be negative
- **Null Safety**: COALESCE functions handle null values gracefully
- **Transaction Safety**: All operations are wrapped in database transactions

### Access Control
- **RLS Policies**: Existing row-level security policies apply
- **Admin Only**: Revenue statistics only visible to authenticated admins
- **API Protection**: All endpoints require admin authentication

## Performance Considerations

### Database Optimization
- **Indexes**: Leverages existing indexes on admin_settings table
- **Efficient Queries**: Single query to fetch all statistics including revenue
- **Caching**: API responses cached to reduce database load

### Scalability
- **Decimal Precision**: DECIMAL(10,2) supports up to ₹99,999,999.99
- **Trigger Efficiency**: Minimal overhead on booking creation
- **Statistics Function**: Optimized single-query approach

## Troubleshooting

### Common Issues

#### Revenue Not Incrementing
1. Check if booking has `payment_status = true`
2. Verify booking has valid `fare` amount
3. Check database trigger is active
4. Review error logs for trigger failures

#### Revenue Display Issues
1. Verify API endpoint returns `currentRevenue` field
2. Check frontend type definitions include `currentRevenue`
3. Ensure proper currency formatting in display

#### Reset Not Working
1. Verify `reset_all_bookings()` function includes revenue reset
2. Check admin authentication for reset API
3. Review database function permissions

### Debugging Commands
```sql
-- Check current revenue
SELECT current_revenue FROM admin_settings WHERE id = 1;

-- Check trigger function
SELECT * FROM information_schema.triggers WHERE trigger_name = 'booking_statistics_trigger';

-- Test revenue increment
INSERT INTO bookings (admission_number, student_name, bus_route, destination, payment_status, fare) 
VALUES ('TEST001', 'Test', 'bus-1', 'Test', true, 100.00);
```

## Future Enhancements

### Potential Improvements
1. **Revenue Analytics**: Daily/weekly/monthly revenue breakdowns
2. **Revenue Reports**: Export revenue data to CSV/PDF
3. **Revenue Goals**: Set and track revenue targets
4. **Revenue Alerts**: Notifications for revenue milestones
5. **Revenue History**: Track revenue changes over time

### Integration Opportunities
1. **Payment Gateway**: Direct integration with payment providers
2. **Accounting Systems**: Export revenue data to accounting software
3. **Financial Reporting**: Generate financial statements
4. **Tax Calculations**: Automatic tax calculations and reporting 