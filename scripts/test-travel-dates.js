#!/usr/bin/env node

/**
 * Test script to verify travel dates are properly stored in bookings
 * 
 * This script:
 * 1. Sets up admin settings with travel dates
 * 2. Creates a test booking
 * 3. Verifies the booking includes the travel dates
 */

const { createClient } = require('@supabase/supabase-js');

// Configuration - replace with your actual values
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Missing required environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function testTravelDates() {
  console.log('🧪 Testing travel dates in bookings...\n');

  try {
    // Step 1: Set up admin settings with travel dates
    console.log('1. Setting up admin settings with travel dates...');
    const { error: settingsError } = await supabase
      .from('admin_settings')
      .upsert({
        id: 1,
        booking_enabled: true,
        go_date: '2024-01-15',
        return_date: '2024-01-20',
        updated_at: new Date().toISOString()
      });

    if (settingsError) {
      throw new Error(`Failed to update admin settings: ${settingsError.message}`);
    }
    console.log('✅ Admin settings updated successfully');

    // Step 2: Create a test booking
    console.log('\n2. Creating test booking...');
    const testBooking = {
      admission_number: 'TEST001',
      student_name: 'Test Student',
      bus_route: 'bus-1',
      destination: 'Kottayam',
      payment_status: false,
      created_at: new Date().toISOString()
    };

    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .insert(testBooking)
      .select()
      .single();

    if (bookingError) {
      throw new Error(`Failed to create booking: ${bookingError.message}`);
    }
    console.log('✅ Test booking created successfully');

    // Step 3: Verify the booking includes travel dates
    console.log('\n3. Verifying travel dates in booking...');
    console.log('Booking data:', {
      id: booking.id,
      student_name: booking.student_name,
      bus_route: booking.bus_route,
      destination: booking.destination,
      go_date: booking.go_date,
      return_date: booking.return_date,
      created_at: booking.created_at
    });

    if (booking.go_date && booking.return_date) {
      console.log('✅ Travel dates are properly stored in the booking!');
      console.log(`   Go date: ${booking.go_date}`);
      console.log(`   Return date: ${booking.return_date}`);
    } else {
      console.log('❌ Travel dates are missing from the booking');
      console.log('   This indicates the migration or API changes may not be working correctly');
    }

    // Step 4: Clean up test data
    console.log('\n4. Cleaning up test data...');
    const { error: deleteError } = await supabase
      .from('bookings')
      .delete()
      .eq('admission_number', 'TEST001');

    if (deleteError) {
      console.log('⚠️  Warning: Failed to clean up test booking:', deleteError.message);
    } else {
      console.log('✅ Test booking cleaned up successfully');
    }

    console.log('\n🎉 Travel dates test completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testTravelDates(); 