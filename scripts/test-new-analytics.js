const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3000';

async function testNewAnalyticsAPI() {
  console.log('🧪 Testing New Analytics Dashboard API Endpoints...\n');

  const endpoints = [
    '/api/admin/analytics/revenue',
    '/api/admin/analytics/routes',
    '/api/admin/analytics/stops',
    '/api/admin/analytics/stops?route=bus-1'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`📡 Testing: ${endpoint}`);
      const response = await fetch(`${BASE_URL}${endpoint}`);
      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log(`✅ Success: ${endpoint}`);
        console.log(`   Data keys: ${Object.keys(data.data).join(', ')}`);
        
        // Show specific data structure for each endpoint
        if (endpoint.includes('revenue')) {
          console.log(`   Total Revenue: ₹${data.data.totalRevenue}`);
          console.log(`   Routes: ${data.data.routes?.length || 0}`);
        } else if (endpoint.includes('routes')) {
          console.log(`   Total Routes: ${data.data.totalRoutes}`);
          console.log(`   Overall Occupancy: ${data.data.overallOccupancyRate}%`);
        } else if (endpoint.includes('stops')) {
          console.log(`   Available Buses: ${data.data.buses?.length || 0}`);
          if (data.data.selectedRouteData) {
            console.log(`   Selected Route: ${data.data.selectedRouteData.selectedBusName}`);
            console.log(`   Total Stops: ${data.data.selectedRouteData.totalStops}`);
          }
        }
      } else {
        console.log(`❌ Failed: ${endpoint}`);
        console.log(`   Error: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${endpoint}`);
      console.log(`   ${error.message}`);
    }
    console.log('');
  }

  console.log('🎉 New Analytics API testing completed!');
}

// Run the test
testNewAnalyticsAPI().catch(console.error); 