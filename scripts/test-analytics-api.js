const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testAnalyticsAPI() {
  console.log('🧪 Testing Analytics Dashboard API Endpoints...\n');

  const endpoints = [
    '/api/admin/reports/revenue',
    '/api/admin/reports/routes',
    '/api/admin/reports/rounds',
    '/api/admin/reports/stops?bus_route=bus-1'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`📡 Testing: ${endpoint}`);
      const response = await fetch(`${BASE_URL}${endpoint}`);
      const data = await response.json();

      if (response.ok && data.success) {
        console.log(`✅ Success: ${endpoint}`);
        console.log(`   Data keys: ${Object.keys(data.data).join(', ')}`);

        // Show sample data structure
        if (endpoint.includes('revenue') && data.data.routes) {
          console.log(`   Total Revenue: ${data.data.totalRevenue}`);
          console.log(`   Routes Count: ${data.data.routes.length}`);
        } else if (endpoint.includes('routes') && data.data.routes) {
          console.log(`   Routes Count: ${data.data.routes.length}`);
        } else if (endpoint.includes('rounds') && data.data.rounds) {
          console.log(`   Rounds Count: ${data.data.rounds.length}`);
        } else if (endpoint.includes('stops') && data.data.stops) {
          console.log(`   Bus: ${data.data.busName}`);
          console.log(`   Stops Count: ${data.data.stops.length}`);
          console.log(`   Total Bookings: ${data.data.totalRouteBookings}`);
        }
      } else {
        console.log(`❌ Failed: ${endpoint}`);
        console.log(`   Status: ${response.status}`);
        console.log(`   Error: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${endpoint}`);
      console.log(`   ${error.message}`);
    }
    console.log('');
  }

  // Test stops endpoint without parameter (should fail)
  try {
    console.log(`📡 Testing: /api/admin/reports/stops (no parameter - should fail)`);
    const response = await fetch(`${BASE_URL}/api/admin/reports/stops`);
    const data = await response.json();

    if (response.status === 400) {
      console.log(`✅ Correctly failed with 400 status`);
      console.log(`   Error: ${data.error}`);
    } else {
      console.log(`❌ Expected 400 status, got ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Error testing stops without parameter`);
    console.log(`   ${error.message}`);
  }

  console.log('\n🎉 Analytics API testing completed!');
}

// Run the test
testAnalyticsAPI().catch(console.error);